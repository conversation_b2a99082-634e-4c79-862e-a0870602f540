"""
云端API客户端
支持与远程业务服务器进行通信
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from core.interfaces import ICloudAPIClient
from core.models import MessageEvent, BaseMessage, PlatformType


@dataclass
class CloudAPIConfig:
    """云端API配置"""
    enabled: bool = False
    base_url: str = ""
    auth_token: str = ""
    timeout: int = 30
    max_retries: int = 3


class CloudAPIClient(ICloudAPIClient):
    """云端API客户端实现"""
    
    def __init__(self, config: CloudAPIConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self._retry_count = 0
        
    async def initialize(self) -> bool:
        """初始化API客户端"""
        if not self.config.enabled:
            return False
            
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.timeout),
                headers={
                    "Authorization": f"Bearer {self.config.auth_token}",
                    "Content-Type": "application/json",
                    "User-Agent": "MultiBot/1.0.0"
                }
            )
            return True
        except Exception as e:
            print(f"初始化云端API客户端失败: {e}")
            return False
    
    async def close(self) -> None:
        """关闭API客户端"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def _make_request(self, method: str, endpoint: str, 
                          data: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """发送API请求"""
        if not self.session:
            await self.initialize()
            if not self.session:
                return None
            
        url = f"{self.config.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        for attempt in range(self.config.max_retries):
            try:
                async with self.session.request(method, url, json=data) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 401:
                        print("云端API认证失败")
                        return None
                    elif response.status >= 500:
                        if attempt < self.config.max_retries - 1:
                            await asyncio.sleep(2 ** attempt)  # 指数退避
                            continue
                    else:
                        print(f"云端API请求失败: {response.status}")
                        return None
                        
            except asyncio.TimeoutError:
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                    continue
                print("云端API请求超时")
                return None
            except Exception as e:
                print(f"云端API请求异常: {e}")
                return None
        
        return None
    
    async def send_message(self, message: BaseMessage) -> Optional[str]:
        """发送消息到云端并返回回复"""
        if not self.config.enabled:
            print(f"[CloudAPI] 云端API未启用")
            return None

        data = {
            "platform": message.platform.value,
            "message": message.content,
            "user_id": message.sender.user_id if message.sender else "",
            "username": message.sender.username if message.sender else "",
            "nickname": message.sender.nickname if message.sender else "",
            "group_id": message.group.group_id if message.group else "",
            "group_name": message.group.group_name if message.group else "",
            "timestamp": message.timestamp.isoformat() if message.timestamp else None
        }

        print(f"[CloudAPI] 发送请求到: {self.config.base_url}/ChatbotController/process")
        print(f"[CloudAPI] 请求数据: {data}")

        result = await self._make_request("POST", "/ChatbotController/process", data)
        print(f"[CloudAPI] 响应结果: {result}")

        # ThinkPHP返回格式：{"success": true, "data": {"should_reply": true, "reply": "..."}, "message": "..."}
        if result and result.get("success") == True:
            data_obj = result.get("data", {})
            if data_obj.get("should_reply") == True:
                return data_obj.get("reply")

        print(f"[CloudAPI] 未获取到有效回复，result: {result}")
        return None
    
    async def get_commands(self) -> List[Dict[str, Any]]:
        """从云端获取命令列表"""
        if not self.config.enabled:
            return []

        result = await self._make_request("GET", "/ChatbotController/config")
        return result.get("features", {}).get("command_prefixes", []) if result else []
    
    async def execute_command(self, command: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """执行云端命令"""
        if not self.config.enabled:
            return None

        data = {
            "platform": params.get("platform", "qq"),
            "message": f"/{command} {params.get('params', '')}".strip(),
            "user_id": params.get("sender_id", ""),
            "group_id": params.get("group_id", "")
        }

        return await self._make_request("POST", "/ChatbotController/process", data)
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.config.enabled:
            return False

        result = await self._make_request("GET", "/ChatbotController/health")
        # ThinkPHP返回格式：{"success": true, "data": {"status": "ok", ...}, "message": "..."}
        return (result is not None and
                result.get("success") == True and
                result.get("data", {}).get("status") == "ok")


class CloudMessageProcessor:
    """云端消息处理器"""
    
    def __init__(self, api_client: CloudAPIClient):
        self.api_client = api_client
    
    def supports_platform(self, platform: PlatformType) -> bool:
        """检查是否支持指定平台"""
        return True  # 云端处理器支持所有平台
    
    async def process_message(self, event: MessageEvent) -> Optional[str]:
        """处理消息"""
        if not self.api_client.config.enabled:
            return None

        try:
            # 只处理命令消息，不处理普通消息
            if self._is_command(event.message.content):
                print(f"[CloudAPI] 处理命令: {event.message.content}")
                # 发送命令消息到云端并获取回复
                reply = await self.api_client.send_message(event.message)
                if reply:
                    print(f"[CloudAPI] 云端回复: {reply[:100]}...")
                    return reply
                else:
                    print(f"[CloudAPI] 云端未返回回复")
                    return None
            else:
                # 普通消息不处理，直接返回None
                print(f"[CloudAPI] 跳过非命令消息: {event.message.content[:50]}...")
                return None

        except Exception as e:
            print(f"[CloudAPI] 云端消息处理失败: {e}")

        return None
    
    def _is_command(self, content: str) -> bool:
        """检查是否是命令"""
        # 使用配置文件中的命令前缀
        command_prefixes = ["/", "!", ".", "#"]  # 默认前缀，实际应该从配置读取
        return any(content.startswith(prefix) for prefix in command_prefixes)

