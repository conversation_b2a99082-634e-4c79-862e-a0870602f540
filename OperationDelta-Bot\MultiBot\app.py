"""
多平台机器人主应用程序
支持同时运行多个平台适配器
"""

import asyncio
import signal
import sys
import inspect
from typing import Dict, List, Optional
from pathlib import Path

from core.models import PlatformType, BaseEvent, MessageEvent
from core.events import global_event_bus, global_event_manager
from core.interfaces import IPlatformAdapter
from adapters.qq import QQAdapter
from adapters.kook import KOOKAdapter
from config import ConfigManager
from utils import setup_logger, get_logger, MessageProcessor
from core.cloud_api import CloudAPIClient, CloudAPIConfig, CloudMessageProcessor
from core.command_processor import create_default_command_processor


class MultiBotApplication:
    """多平台机器人应用程序"""
    
    def __init__(self, config_file: Optional[str] = None):
        # 初始化配置管理器
        self.config_manager = ConfigManager(config_file)
        
        # 初始化日志系统
        global_config = self.config_manager.get_global_config()
        self.logger = setup_logger(
            name=global_config.get("app_name", "MultiBot"),
            level=global_config.get("log_level", "INFO"),
            log_file=global_config.get("log_file"),
            max_size=global_config.get("log_max_size", 10*1024*1024),
            backup_count=global_config.get("log_backup_count", 5)
        )
        
        # 应用状态
        self.running = False
        self.shutdown_event = asyncio.Event()
        
        # 平台适配器
        self.adapters: Dict[PlatformType, IPlatformAdapter] = {}
        
        # 消息处理器
        self.message_processor: MessageProcessor = None
        
        # 命令处理器
        self.command_processor = None
        
        self.logger.info(f"多平台机器人应用程序初始化完成")
    
    async def initialize(self) -> bool:
        """初始化应用程序"""
        try:
            self.logger.info("正在初始化多平台机器人应用程序...")
            
            # 初始化消息处理器
            self._initialize_message_processor()
            
            # 初始化命令处理器
            self._initialize_command_processor()
            
            # 初始化平台适配器
            await self._initialize_adapters()
            
            # 注册事件处理器
            self._register_event_handlers()
            
            self.logger.info("多平台机器人应用程序初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"应用程序初始化失败: {e}", exc_info=True)
            return False
    
    def _initialize_message_processor(self) -> None:
        """初始化消息处理器"""
        global_config = self.config_manager.get_global_config()
        
        if global_config.get("cloud_api_enabled", False):
            # 使用云端消息处理器
            cloud_config = CloudAPIConfig(
                enabled=global_config.get("cloud_api_enabled", False),
                base_url=global_config.get("cloud_api_base_url", ""),
                auth_token=global_config.get("cloud_api_auth_token", ""),
                timeout=global_config.get("cloud_api_timeout", 30),
                max_retries=global_config.get("cloud_api_max_retries", 3)
            )
            
            cloud_client = CloudAPIClient(cloud_config)
            self.message_processor = CloudMessageProcessor(cloud_client)
            self.logger.info("使用云端消息处理器")
        else:
            # 使用本地消息处理器
            self.message_processor = MessageProcessor()
            self.logger.info("使用本地消息处理器")
    
    def _initialize_command_processor(self) -> None:
        """初始化命令处理器"""
        global_config = self.config_manager.get_global_config()
        prefixes = global_config.get("command_prefixes", ["/", "!", ".", "#"])
        
        self.command_processor = create_default_command_processor()
        self.command_processor.prefixes = prefixes
        self.logger.info(f"命令处理器初始化完成，支持前缀: {prefixes}")
    
    async def _initialize_adapters(self) -> None:
        """初始化平台适配器"""
        enabled_platforms = self.config_manager.get_enabled_platforms()
        
        for platform in enabled_platforms:
            try:
                adapter = self._create_adapter(platform)
                if adapter:
                    # 设置事件总线
                    adapter.set_event_bus(global_event_bus)
                    
                    # 初始化适配器
                    platform_config = self.config_manager.get_platform_config(platform)
                    if await adapter.initialize(platform_config):
                        self.adapters[platform] = adapter
                        self.logger.info(f"{platform.value} 适配器初始化成功")
                    else:
                        self.logger.error(f"{platform.value} 适配器初始化失败")
                
            except Exception as e:
                self.logger.error(f"初始化 {platform.value} 适配器失败: {e}", exc_info=True)
        
        if not self.adapters:
            raise Exception("没有可用的平台适配器")
    
    def _create_adapter(self, platform: PlatformType) -> Optional[IPlatformAdapter]:
        """创建平台适配器"""
        if platform == PlatformType.QQ:
            return QQAdapter(self.logger)
        elif platform == PlatformType.KOOK:
            return KOOKAdapter(self.logger)
        else:
            self.logger.warning(f"不支持的平台类型: {platform}")
            return None
    
    def _register_event_handlers(self) -> None:
        """注册事件处理器"""
        try:
            # 注册消息事件处理器
            global_event_bus.subscribe_all(self._handle_event)
            
            self.logger.info("事件处理器注册完成")
            
        except Exception as e:
            self.logger.error(f"事件处理器注册失败: {e}")
    
    async def _handle_event(self, event: BaseEvent) -> None:
        """处理事件"""
        try:
            self.logger.info(f"收到事件: {type(event).__name__}, event_type={event.event_type}, platform={event.platform}")
            
            if isinstance(event, MessageEvent):
                await self._handle_message_event(event)
            else:
                # 处理其他类型的事件
                self.logger.debug(f"收到事件: {event}")
                
        except Exception as e:
            self.logger.error(f"处理事件失败: {e}", exc_info=True)
    
    async def _handle_message_event(self, event: MessageEvent) -> None:
        """处理消息事件"""
        try:
            message = event.message
            platform = event.platform
            
            # 记录消息
            sender_info = str(message.sender) if message.sender else "Unknown"
            group_info = f" in {message.group}" if message.group else ""
            self.logger.info(f"[{platform.value.upper()}] {sender_info}{group_info}: {message.content[:100]}...")
            self.logger.info(f"[{platform.value.upper()}] 消息类型: is_private={message.is_private}, is_group={message.is_group}, is_channel={message.is_channel}")
            
            # 处理消息
            reply = None

            # 所有消息都通过云端消息处理器处理（包括命令）
            if self.message_processor and hasattr(self.message_processor, 'supports_platform') and self.message_processor.supports_platform(platform):
                reply = await self.message_processor.process_message(event)
                if reply:
                    self.logger.info(f"[{platform.value.upper()}] 云端处理结果: {reply[:50]}...")

            # 如果云端处理器没有回复且是命令，记录但不使用本地备用处理器
            if not reply and self.command_processor and self.command_processor.is_command(message.content):
                self.logger.warning(f"[{platform.value.upper()}] 云端处理器未返回回复，命令: {message.content[:50]}...")
                # 可选：完全禁用本地备用处理
                # reply = await self.command_processor.execute_command(event)
                # if reply:
                #     self.logger.info(f"[{platform.value.upper()}] 本地备用处理结果: {reply[:50]}...")
            
            # 发送回复
            if reply:
                await self._send_reply(event, reply)
            else:
                self.logger.debug(f"消息处理完成，无回复")
            
        except Exception as e:
            self.logger.error(f"处理消息事件失败: {e}", exc_info=True)
    
    async def _send_reply(self, event: MessageEvent, reply: str) -> None:
        """发送回复消息"""
        try:
            message = event.message
            platform = event.platform
            
            if platform not in self.adapters:
                self.logger.warning(f"平台 {platform.value} 的适配器不可用")
                return
            
            adapter = self.adapters[platform]
            
            # 确定回复目标
            if message.is_private:
                target_type = "private"
                target_id = message.sender.user_id if message.sender else ""
            elif message.is_group or message.is_channel:
                target_type = "group" if platform == PlatformType.QQ else "channel"

                # 对于KOOK，需要使用频道ID而不是服务器ID
                if platform == PlatformType.KOOK and hasattr(message, 'channel_id') and message.channel_id:
                    target_id = message.channel_id
                else:
                    target_id = message.group.group_id if message.group else ""
            else:
                self.logger.warning("无法确定回复目标")
                return
            
            # 发送回复
            success = await adapter.send_message(target_type, target_id, reply)
            if success:
                self.logger.info(f"[{platform.value.upper()}] 回复消息: {reply[:50]}...")
            else:
                self.logger.warning(f"[{platform.value.upper()}] 发送回复失败")
                
        except Exception as e:
            self.logger.error(f"发送回复失败: {e}", exc_info=True)
    
    async def start(self) -> None:
        """启动应用程序"""
        try:
            self.running = True
            self.logger.info("多平台机器人应用程序启动")
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            # 启动所有适配器
            adapter_tasks = []
            for platform, adapter in self.adapters.items():
                task = asyncio.create_task(adapter.start())
                adapter_tasks.append(task)
                self.logger.info(f"启动 {platform.value} 适配器")
            
            # 等待关闭信号
            shutdown_task = asyncio.create_task(self.shutdown_event.wait())
            
            # 等待任一任务完成
            done, pending = await asyncio.wait(
                adapter_tasks + [shutdown_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # 检查适配器任务是否有异常
            for task in done:
                if task.exception():
                    self.logger.error(f"适配器任务异常: {task.exception()}")
            
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"应用程序运行异常: {e}", exc_info=True)
        finally:
            await self.shutdown()
    
    def _setup_signal_handlers(self) -> None:
        """设置信号处理器"""
        if sys.platform != 'win32':
            # Unix系统信号处理
            loop = asyncio.get_event_loop()
            
            def signal_handler():
                self.logger.info("收到关闭信号")
                self.shutdown_event.set()
            
            loop.add_signal_handler(signal.SIGINT, signal_handler)
            loop.add_signal_handler(signal.SIGTERM, signal_handler)
        else:
            # Windows系统使用不同的方式
            def signal_handler(signum, _):
                self.logger.info(f"收到信号 {signum}")
                asyncio.create_task(self._async_shutdown())
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
    
    async def _async_shutdown(self) -> None:
        """异步关闭"""
        self.shutdown_event.set()
    
    async def shutdown(self) -> None:
        """关闭应用程序"""
        try:
            self.running = False
            self.logger.info("正在关闭多平台机器人应用程序...")
            
            # 停止所有适配器
            for platform, adapter in self.adapters.items():
                try:
                    await adapter.stop()
                    self.logger.info(f"{platform.value} 适配器已停止")
                except Exception as e:
                    self.logger.error(f"停止 {platform.value} 适配器失败: {e}")
            
            self.logger.info("多平台机器人应用程序已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭应用程序时发生错误: {e}", exc_info=True)
    
    def get_status(self) -> Dict[str, any]:
        """获取应用程序状态"""
        adapter_status = {}
        for platform, adapter in self.adapters.items():
            adapter_status[platform.value] = adapter.get_status()
        
        return {
            "running": self.running,
            "enabled_platforms": [p.value for p in self.adapters.keys()],
            "adapters": adapter_status,
            "config_file": self.config_manager.config_file
        }
